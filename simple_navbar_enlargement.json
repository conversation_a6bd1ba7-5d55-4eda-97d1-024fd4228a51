{"code_fixes": [{"file_path": "/var/www/html/assets/css/_layout/site-nav.less", "modified_content": "@import (reference) '../_variables';\n\n.site-nav {\n  background: var(--site-nav-bg, var(--primary));\n\n  // overlap box-shadows from main content/sidebar\n  z-index: 5;\n\n  &__container {\n    display: flex;\n    width: 100%;\n  }\n\n  &__link {\n    color: var(--primary-fg);\n    cursor: pointer;\n    display: inline-block;\n    font-size: 1.5em; // 放大字体 - 原来是 @nav-font-size\n    padding: 1.2em 1.5em; // 增加内边距 - 原来是 0.875em 1em\n    position: relative;\n\n    &:focus {\n      background-color: var(--primary-alt);\n      outline: none;\n    }\n\n    &:hover {\n      text-decoration: none;\n    }\n\n    &:hover::after,\n    &--active::after {\n      background-color: var(--primary-fg);\n      bottom: 1px;\n      content: '';\n      height: 3px; // 增加下划线厚度 - 原来是 2px\n      left: 0;\n      position: absolute;\n      width: 100%;\n    }\n  }\n\n  &__has-notifications {\n    background-color: var(--notification);\n  }\n\n  &__search-input {\n    border-color: transparent !important;\n    border-radius: 8rem !important;\n    padding-left: 2.5rem !important;\n    width: 100% !important;\n\n    &:not(:focus) {\n      background-color: transparent !important;\n      border-color: var(--primary-fg) !important;\n      color: var(--primary-fg) !important;\n    }\n  }\n\n  &__search-label {\n    cursor: text;\n    padding: calc(0.5rem + 1px) calc(0.75rem + 1px);\n    position: absolute;\n    top: 0;\n    left: 0;\n    z-index: 1;\n\n    .icon {\n      color: var(--primary-fg);\n    }\n  }\n\n  &__search-row {\n    position: relative;\n  }\n\n  &__list {\n    display: flex;\n    flex-wrap: wrap;\n    margin: 0;\n    padding: 0;\n  }\n\n  &__item {\n    display: flex;\n    position: relative;\n  }\n\n  &__item--search {\n    flex: 1;\n    min-width: 12rem;\n  }\n\n  &__item--user {\n    margin-left: auto;\n  }\n\n  &__item--user &__link {\n    padding-left: 0.5rem;\n    padding-right: 0.5rem;\n  }\n\n  &__item--user &__link .icon {\n    margin-right: 0.25rem;\n  }\n\n  &__item--user &__link .icon:only-child {\n    margin-right: 0;\n  }\n\n  &__item--user &__link .icon + .icon {\n    margin-left: 0.25rem;\n  }\n\n  &__item--user &__link .icon + .icon:last-child {\n    margin-right: 0;\n  }\n\n  &__item--user &__link .icon + .icon:not(:last-child) {\n    margin-right: 0.25rem;\n  }\n\n  &__item--user &__link .icon:first-child:not(:only-child) {\n    margin-right: 0.25rem;\n  }\n\n  &__item--user &__link .icon:last-child:not(:only-child) {\n    margin-left: 0.25rem;\n  }\n\n  &__item--user &__link .icon:not(:first-child):not(:last-child) {\n    margin-left: 0.25rem;\n    margin-right: 0.25rem;\n  }\n\n  &__item--user &__link .icon:only-child {\n    margin: 0;\n  }\n\n  &__item--user &__link .icon + .text {\n    margin-left: 0.25rem;\n  }\n\n  &__item--user &__link .text + .icon {\n    margin-left: 0.25rem;\n  }\n\n  &__item--user &__link .text:only-child {\n    margin: 0;\n  }\n\n  &__item--user &__link .text + .text {\n    margin-left: 0.25rem;\n  }\n\n  &__item--user &__link .text:first-child:not(:only-child) {\n    margin-right: 0.25rem;\n  }\n\n  &__item--user &__link .text:last-child:not(:only-child) {\n    margin-left: 0.25rem;\n  }\n\n  &__item--user &__link .text:not(:first-child):not(:last-child) {\n    margin-left: 0.25rem;\n    margin-right: 0.25rem;\n  }\n\n  @media (max-width: 767px) {\n    &__container {\n      flex-direction: column;\n    }\n\n    &__list {\n      border-top: 1px solid var(--primary-alt);\n      flex-direction: column;\n    }\n\n    &__item {\n      border-bottom: 1px solid var(--primary-alt);\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &--search {\n        border-bottom: none;\n        order: -1;\n      }\n\n      &--user {\n        margin-left: 0;\n      }\n    }\n\n    &__link {\n      padding: 1rem;\n      width: 100%;\n\n      &:hover::after,\n      &--active::after {\n        bottom: 0;\n        left: 0;\n        top: 0;\n        width: 2px;\n        height: 100%;\n      }\n    }\n\n    &__link {\n      display: block;\n    }\n\n    &__search-row {\n      padding: 0.5rem 1rem;\n    }\n  }\n\n  .no-js:root :hover > &__link.dropdown__toggle,\n  .js:root .dropdown--expanded > &__link.dropdown__toggle {\n    background-color: var(--primary-alt);\n  }\n}"}]}