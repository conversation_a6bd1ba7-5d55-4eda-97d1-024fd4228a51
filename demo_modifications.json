{"code_fixes": [{"file_path": "/var/www/html/assets/css/_widgets/button.less", "modification_type": "append", "content": "\n\n// Demo: Enhanced button styles for webpage revision demo\n.demo-enhanced-button {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\n  }\n  \n  &:active {\n    transform: translateY(0);\n    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);\n  }\n}\n\n.demo-action-button {\n  background: #ff6b6b;\n  color: white;\n  border: 2px solid #ff5252;\n  padding: 10px 20px;\n  border-radius: 25px;\n  font-size: 14px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background: #ff5252;\n    border-color: #ff1744;\n    transform: scale(1.05);\n  }\n}\n\n.demo-mega-button {\n  background: #4caf50;\n  color: white;\n  border: none;\n  padding: 20px 40px;\n  border-radius: 12px;\n  font-size: 20px;\n  font-weight: 700;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);\n  \n  &:hover {\n    background: #45a049;\n    transform: translateY(-3px) scale(1.02);\n    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.4);\n  }\n}"}, {"file_path": "/var/www/html/assets/css/_layout/site-nav.less", "modification_type": "append", "content": "\n\n// Demo: Enhanced navigation for webpage revision demo\n.demo-enhanced-nav {\n  .site-nav {\n    background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n    \n    .site-nav__list {\n      .site-nav__item {\n        margin: 0 5px;\n        \n        .site-nav__link {\n          color: white;\n          padding: 12px 20px;\n          border-radius: 8px;\n          transition: all 0.3s ease;\n          font-weight: 500;\n          \n          &:hover {\n            background: rgba(255, 255, 255, 0.2);\n            transform: translateY(-2px);\n            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n          }\n        }\n      }\n    }\n  }\n}\n\n// Demo: Enlarged menu items\n.demo-large-menu {\n  .menu-item {\n    font-size: 18px !important;\n    padding: 15px 25px !important;\n    margin: 5px 0 !important;\n    border-radius: 8px !important;\n    transition: all 0.2s ease !important;\n    \n    &:hover {\n      background: #f0f0f0 !important;\n      transform: scale(1.02) !important;\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;\n    }\n  }\n}\n\n// Demo: Mega dropdown menu\n.demo-mega-dropdown {\n  .dropdown {\n    .dropdown__menu {\n      min-width: 400px !important;\n      padding: 20px !important;\n      border-radius: 12px !important;\n      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2) !important;\n      background: white !important;\n      border: 2px solid #e0e0e0 !important;\n      \n      .menu-item {\n        font-size: 16px !important;\n        padding: 12px 20px !important;\n        margin: 3px 0 !important;\n        border-radius: 6px !important;\n        \n        &:hover {\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\n          color: white !important;\n          transform: translateX(5px) !important;\n        }\n      }\n    }\n  }\n}"}, {"file_path": "/var/www/html/assets/css/_utilities/demo-enhancements.less", "modification_type": "create", "content": "// Demo: Special enhancements for webpage revision demonstration\n\n// Animated background\n.demo-animated-bg {\n  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);\n  background-size: 400% 400%;\n  animation: gradientShift 15s ease infinite;\n}\n\n@keyframes gradientShift {\n  0% {\n    background-position: 0% 50%;\n  }\n  50% {\n    background-position: 100% 50%;\n  }\n  100% {\n    background-position: 0% 50%;\n  }\n}\n\n// Floating action button\n.demo-fab {\n  position: fixed;\n  bottom: 30px;\n  right: 30px;\n  width: 60px;\n  height: 60px;\n  background: #ff4081;\n  border-radius: 50%;\n  border: none;\n  color: white;\n  font-size: 24px;\n  cursor: pointer;\n  box-shadow: 0 6px 20px rgba(255, 64, 129, 0.4);\n  transition: all 0.3s ease;\n  z-index: 1000;\n  \n  &:hover {\n    transform: scale(1.1) rotate(90deg);\n    box-shadow: 0 8px 30px rgba(255, 64, 129, 0.6);\n  }\n}\n\n// Enhanced cards\n.demo-enhanced-card {\n  border-radius: 15px !important;\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;\n  transition: all 0.3s ease !important;\n  border: 1px solid #e0e0e0 !important;\n  \n  &:hover {\n    transform: translateY(-5px) !important;\n    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15) !important;\n  }\n}\n\n// Glowing text effect\n.demo-glow-text {\n  color: #fff;\n  text-shadow: 0 0 10px #00ff00, 0 0 20px #00ff00, 0 0 30px #00ff00;\n  animation: glow 2s ease-in-out infinite alternate;\n}\n\n@keyframes glow {\n  from {\n    text-shadow: 0 0 10px #00ff00, 0 0 20px #00ff00, 0 0 30px #00ff00;\n  }\n  to {\n    text-shadow: 0 0 20px #00ff00, 0 0 30px #00ff00, 0 0 40px #00ff00;\n  }\n}\n\n// Pulse animation\n.demo-pulse {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n// Enlarged interactive elements\n.demo-large-interactive {\n  font-size: 120% !important;\n  padding: 1.5em !important;\n  margin: 1em 0 !important;\n  \n  input, button, select, textarea {\n    font-size: 118% !important;\n    padding: 12px 16px !important;\n    border-radius: 8px !important;\n  }\n}\n\n// Rainbow border effect\n.demo-rainbow-border {\n  border: 3px solid;\n  border-image: linear-gradient(45deg, red, orange, yellow, green, blue, indigo, violet) 1;\n  animation: rainbow-rotate 3s linear infinite;\n}\n\n@keyframes rainbow-rotate {\n  0% { filter: hue-rotate(0deg); }\n  100% { filter: hue-rotate(360deg); }\n}"}, {"file_path": "/var/www/html/assets/css/core.less", "modification_type": "append", "content": "\n\n// Demo: Import demo enhancements\n@import '_utilities/demo-enhancements';"}]}