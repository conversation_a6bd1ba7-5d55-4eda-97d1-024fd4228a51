{"code_fixes": [{"file_path": "/var/www/html/assets/css/core.less", "modified_content": "@import (less) '~normalize.css';\n\n/*! https://postmill.xyz/ */\n\n// the order of stuff here is important. don't change things around\n// unnecessarily.\n\n@import '_global';\n\n@import '_resets/unbuttonize';\n@import '_resets/undecorate';\n@import '_resets/unheaderize';\n@import '_resets/unlistify';\n\n@import '_form/compound-form-widget';\n@import '_form/decorated-form-control';\n@import '_form/fieldset';\n@import '_form/form'; // DEPRECATED\n@import '_form/form-control';\n@import '_form/form-error-list';\n@import '_form/form-flex';\n@import '_form/form-tabs';\n@import '_form/formatting-help';\n@import '_form/markdown-preview';\n@import '_form/unstylable-widget';\n\n@import '_widgets/button'; // must come before other widget types\n@import '_widgets/clear-notification-button';\n@import '_widgets/discreet-tab';\n@import '_widgets/dropdown';\n@import '_widgets/menu-item';\n@import '_widgets/subscribe-button';\n@import '_widgets/tab';\n\n@import '_layout/content-container';\n@import '_layout/flow';\n@import '_layout/site-accessibility-nav';\n@import '_layout/site-alerts';\n@import '_layout/site-content';\n@import '_layout/site-footer';\n@import '_layout/site-nav';\n@import '_layout/sidebar';\n@import '_layout/text-flow';\n\n@import '_card/dropdown-card';\n\n@import '_things/alert';\n@import '_things/border-list';\n@import '_things/columns';\n@import '_things/comment';\n@import '_things/definition-list';\n@import '_things/drop-zone';\n@import '_things/empty';\n@import '_things/flair';\n@import '_things/flex';\n@import '_things/heading-permalink';\n@import '_things/hideable';\n@import '_things/icon';\n@import '_things/message';\n@import '_things/submission';\n@import '_things/submission-meta';\n@import '_things/table';\n@import '_things/table-of-contents';\n@import '_things/user-flag';\n@import '_things/vote';\n@import '_things/wiki-article';\n@import '_things/wiki-lock-notice';\n\n// overriding modifier classes\n@import '_utilities/block';\n@import '_utilities/break-text';\n@import '_utilities/colors';\n@import '_utilities/font-weight';\n@import '_utilities/hidden';\n@import '_utilities/inline';\n@import '_utilities/monospace';\n@import '_utilities/night-mode';\n@import '_utilities/no-desktop';\n@import '_utilities/no-mobile';\n@import '_utilities/no-underline';\n@import '_utilities/no-select';\n@import '_utilities/no-visibility';\n@import '_utilities/no-wrap';\n@import '_utilities/pad';\n@import '_utilities/page-shadow';\n@import '_utilities/text-align';\n@import '_utilities/text-size';\n\n@import '_vendor/hljs';\n@import '_vendor/select2';\n@import '_vendor/tippy';\n\n// Demo: Import demo enhancements for webpage revision demo\n@import '_utilities/demo-enhancements';"}]}