{"code_fixes": [{"file_path": "/var/www/html/assets/css/_widgets/button.less", "modified_content": "@import '_mixins';\n\n.button {\n  .button-common();\n\n  &:focus,\n  &:hover {\n    .button-active();\n  }\n\n  &--small {\n    .button-small();\n  }\n\n  &--secondary {\n    .button-secondary();\n  }\n\n  &--transparent {\n    .button-transparent();\n  }\n\n  &:disabled {\n    .button-disabled();\n  }\n\n  &--flex {\n    display: inline-flex;\n    justify-content: center;\n  }\n\n  &--flex > * + * {\n    margin-left: 0.5em;\n  }\n}\n\n// Demo: Enhanced button styles for webpage revision demo\n.demo-enhanced-button {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 15px 30px;\n  border-radius: 10px;\n  font-size: 18px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  \n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\n  }\n}\n\n.demo-mega-button {\n  background: #4caf50;\n  color: white;\n  border: none;\n  padding: 25px 50px;\n  border-radius: 15px;\n  font-size: 24px;\n  font-weight: 700;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);\n  \n  &:hover {\n    background: #45a049;\n    transform: translateY(-4px) scale(1.05);\n    box-shadow: 0 12px 35px rgba(76, 175, 80, 0.4);\n  }\n}"}, {"file_path": "/var/www/html/assets/css/_utilities/demo-enhancements.less", "modified_content": "// Demo: Special enhancements for webpage revision demonstration\n\n// Enlarged menu items\n.demo-large-menu {\n  .menu-item {\n    font-size: 20px !important;\n    padding: 18px 30px !important;\n    margin: 8px 0 !important;\n    border-radius: 10px !important;\n    transition: all 0.2s ease !important;\n    \n    &:hover {\n      background: #f0f0f0 !important;\n      transform: scale(1.05) !important;\n      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;\n    }\n  }\n}\n\n// Mega dropdown menu\n.demo-mega-dropdown {\n  .dropdown {\n    .dropdown__menu {\n      min-width: 500px !important;\n      padding: 25px !important;\n      border-radius: 15px !important;\n      box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2) !important;\n      background: white !important;\n      border: 3px solid #e0e0e0 !important;\n      \n      .menu-item {\n        font-size: 18px !important;\n        padding: 15px 25px !important;\n        margin: 5px 0 !important;\n        border-radius: 8px !important;\n        \n        &:hover {\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\n          color: white !important;\n          transform: translateX(8px) !important;\n        }\n      }\n    }\n  }\n}\n\n// Floating action button\n.demo-fab {\n  position: fixed;\n  bottom: 30px;\n  right: 30px;\n  width: 70px;\n  height: 70px;\n  background: #ff4081;\n  border-radius: 50%;\n  border: none;\n  color: white;\n  font-size: 28px;\n  cursor: pointer;\n  box-shadow: 0 8px 25px rgba(255, 64, 129, 0.4);\n  transition: all 0.3s ease;\n  z-index: 1000;\n  \n  &:hover {\n    transform: scale(1.2) rotate(180deg);\n    box-shadow: 0 12px 40px rgba(255, 64, 129, 0.6);\n  }\n}\n\n// Enhanced cards\n.demo-enhanced-card {\n  border-radius: 20px !important;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;\n  transition: all 0.3s ease !important;\n  border: 2px solid #e0e0e0 !important;\n  \n  &:hover {\n    transform: translateY(-8px) !important;\n    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15) !important;\n  }\n}\n\n// Pulse animation\n.demo-pulse {\n  animation: pulse 1.5s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n// Enlarged interactive elements\n.demo-large-interactive {\n  font-size: 130% !important;\n  padding: 2em !important;\n  margin: 1.5em 0 !important;\n  \n  input, button, select, textarea {\n    font-size: 125% !important;\n    padding: 15px 20px !important;\n    border-radius: 10px !important;\n  }\n}"}]}